using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;
using ZinStore.Business.Helpers;

namespace ZinStore.Business.Services
{
    public class ProduitService
    {
        private readonly ProduitRepository _produitRepository;
        private readonly CategorieRepository _categorieRepository;

        public ProduitService(DatabaseContext context)
        {
            _produitRepository = new ProduitRepository(context);
            _categorieRepository = new CategorieRepository(context);
        }

        public async Task<IEnumerable<Produit>> GetAllProduitsAsync()
        {
            return await _produitRepository.GetAllAsync();
        }

        public async Task<Produit> GetProduitByIdAsync(int id)
        {
            return await _produitRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<Produit>> SearchProduitsAsync(string searchTerm)
        {
            return await _produitRepository.SearchAsync(searchTerm);
        }

        public async Task<IEnumerable<Produit>> GetProduitsEnRuptureAsync()
        {
            return await _produitRepository.GetProduitsEnRuptureAsync();
        }

        public async Task<(bool Success, string Message, int ProduitId)> AddProduitAsync(Produit produit)
        {
            try
            {
                var validationResult = await ValidateProduitAsync(produit);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage, 0);
                }

                CleanProduitData(produit);
                int produitId = await _produitRepository.AddAsync(produit);
                return (true, "Produit ajouté avec succès.", produitId);
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de l'ajout du produit: {ex.Message}", 0);
            }
        }

        public async Task<string> GenerateNextCodeAsync()
        {
            return await _produitRepository.GenerateNextCodeAsync("PR");
        }

        private async Task<(bool IsValid, string ErrorMessage)> ValidateProduitAsync(Produit produit)
        {
            if (produit == null)
                return (false, "Les données du produit sont requises.");

            if (string.IsNullOrWhiteSpace(produit.CodeProduit))
                return (false, "Le code produit est obligatoire.");

            if (string.IsNullOrWhiteSpace(produit.Nom))
                return (false, "Le nom du produit est obligatoire.");

            if (produit.CategorieId <= 0)
                return (false, "La catégorie est obligatoire.");

            if (!ValidationHelper.IsValidAmount(produit.PrixAchat))
                return (false, "Le prix d'achat doit être positif.");

            if (!ValidationHelper.IsValidAmount(produit.PrixVente))
                return (false, "Le prix de vente doit être positif.");

            // Vérifier que la catégorie existe
            var categorie = await _categorieRepository.GetByIdAsync(produit.CategorieId);
            if (categorie == null)
                return (false, "La catégorie sélectionnée n'existe pas.");

            return (true, string.Empty);
        }

        private void CleanProduitData(Produit produit)
        {
            produit.CodeProduit = ValidationHelper.CleanCode(produit.CodeProduit);
            produit.Nom = produit.Nom?.Trim();
            produit.Description = produit.Description?.Trim();
        }
    }
}
