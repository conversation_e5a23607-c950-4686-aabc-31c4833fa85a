<Window x:Class="ZinStore.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
        Title="ZinStore - Gestion de Supermarché"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewModels:MainViewModel />
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Barre de menu -->
        <materialDesign:Card Grid.Row="0"
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Margin="0,0,0,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo et titre -->
                <StackPanel Grid.Column="0"
                           Orientation="Horizontal"
                           Margin="20,10">
                    <materialDesign:PackIcon Kind="Store"
                                           Width="32"
                                           Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="ZinStore"
                              FontSize="20"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <!-- Menu principal -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal"
                           HorizontalAlignment="Center"
                           Margin="20,10">

                    <Button Content="Tableau de Bord"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowDashboardCommand}"
                           Margin="5,0"/>

                    <Button Content="Clients"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowClientsCommand}"
                           Margin="5,0"/>

                    <Button Content="Fournisseurs"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowFournisseursCommand}"
                           Margin="5,0"/>

                    <Button Content="Produits"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowProduitsCommand}"
                           Margin="5,0"/>

                    <Button Content="Ventes"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowVentesCommand}"
                           Margin="5,0"/>

                    <Button Content="Achats"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowAchatsCommand}"
                           Margin="5,0"/>

                    <Button Content="Stock"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowStockCommand}"
                           Margin="5,0"/>

                    <Button Content="Rapports"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Command="{Binding ShowRapportsCommand}"
                           Margin="5,0"/>

                    <!-- Menu Paramètres -->
                    <Button x:Name="SettingsButton"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Margin="5,0"
                           Click="SettingsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Settings"
                                                   Width="16" Height="16"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,5,0"/>
                            <TextBlock Text="Paramètres" VerticalAlignment="Center"/>
                        </StackPanel>
                        <Button.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="Configuration Base de Données"
                                         Click="DatabaseConfig_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Database"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="Paramètres Généraux"
                                         Click="GeneralSettings_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Cog"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="À propos"
                                         Click="About_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Information"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </Button.ContextMenu>
                    </Button>
                </StackPanel>

                <!-- Informations utilisateur -->
                <StackPanel Grid.Column="2"
                           Orientation="Horizontal"
                           Margin="20,10">
                    <materialDesign:PackIcon Kind="Account"
                                           Width="24"
                                           Height="24"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding CurrentUserName}"
                              VerticalAlignment="Center"
                              Margin="0,0,10,0"/>
                    <Button Content="Déconnexion"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding LogoutCommand}"
                           Height="30"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Contenu principal -->
        <ContentControl Grid.Row="1"
                       Content="{Binding CurrentView}"
                       Margin="10"/>

        <!-- Barre d'état -->
        <materialDesign:Card Grid.Row="2"
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="0,5,0,0">
            <Grid Margin="20,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="{Binding StatusMessage}"
                          VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1"
                          Text="{Binding CurrentDateTime, StringFormat='dd/MM/yyyy HH:mm'}"
                          VerticalAlignment="Center"
                          Margin="0,0,20,0"/>

                <TextBlock Grid.Column="2"
                          Text="Version 1.0"
                          VerticalAlignment="Center"
                          Opacity="0.6"/>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
