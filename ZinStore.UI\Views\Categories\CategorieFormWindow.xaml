<Window x:Class="ZinStore.UI.Views.Categories.CategorieFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Gestion Catégorie - ZinStore"
        Height="800"
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="FolderMultiple" 
                                   Width="48" Height="48"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   HorizontalAlignment="Center"/>
            <TextBlock x:Name="TitleText"
                      Text="Nouvelle Catégorie"
                      FontSize="18"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,10,0,0"/>
        </StackPanel>

        <!-- Formulaire -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card Padding="20">
                <StackPanel>
                    <!-- Informations de base -->
                    <TextBlock Text="Informations de base" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Code Catégorie -->
                    <TextBox x:Name="CodeCategorieTextBox"
                            materialDesign:HintAssist.Hint="Code Catégorie *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Nom -->
                    <TextBox x:Name="NomTextBox"
                            materialDesign:HintAssist.Hint="Nom de la Catégorie *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Description -->
                    <TextBox x:Name="DescriptionTextBox"
                            materialDesign:HintAssist.Hint="Description"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Paramètres d'affichage -->
                    <TextBlock Text="Paramètres d'Affichage" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Couleur et Icône -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="CouleurTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Couleur (ex: #FF5722)"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="#2196F3"/>
                        
                        <TextBox x:Name="IconeTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Icône"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="FolderMultiple"/>
                    </Grid>

                    <!-- Ordre d'affichage -->
                    <TextBox x:Name="OrdreAffichageTextBox"
                            materialDesign:HintAssist.Hint="Ordre d'Affichage"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Text="1"
                            Margin="0,0,0,15"/>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Options -->
                    <TextBlock Text="Options" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Catégorie active -->
                    <CheckBox x:Name="EstActiveCheckBox"
                             Content="Catégorie active"
                             IsChecked="True"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,0,15"/>

                    <!-- Afficher dans le menu -->
                    <CheckBox x:Name="AfficherDansMenuCheckBox"
                             Content="Afficher dans le menu principal"
                             IsChecked="True"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,0,15"/>

                    <!-- Autoriser sous-catégories -->
                    <CheckBox x:Name="AutoriserSousCategoriesCheckBox"
                             Content="Autoriser les sous-catégories"
                             IsChecked="False"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,0,15"/>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Métadonnées -->
                    <TextBlock Text="Métadonnées" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Mots-clés -->
                    <TextBox x:Name="MotsClesTextBox"
                            materialDesign:HintAssist.Hint="Mots-clés (séparés par des virgules)"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Notes -->
                    <TextBox x:Name="NotesTextBox"
                            materialDesign:HintAssist.Hint="Notes internes"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="60"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>

                    <!-- Aperçu de la catégorie -->
                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                           BorderThickness="1"
                           CornerRadius="4"
                           Padding="15"
                           Margin="0,10,0,0">
                        <StackPanel>
                            <TextBlock Text="Aperçu" 
                                      FontWeight="Bold" 
                                      FontSize="12" 
                                      Margin="0,0,0,10"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <materialDesign:PackIcon x:Name="PreviewIcon"
                                                       Grid.Column="0"
                                                       Kind="FolderMultiple"
                                                       Width="24" Height="24"
                                                       Foreground="#2196F3"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,10,0"/>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock x:Name="PreviewNom"
                                              Text="Nom de la catégorie"
                                              FontWeight="Bold"
                                              FontSize="14"/>
                                    <TextBlock x:Name="PreviewDescription"
                                              Text="Description de la catégorie"
                                              FontSize="12"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            <Button Content="Annuler"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click"
                   Width="100"
                   Margin="0,0,10,0"/>
            <Button x:Name="SaveButton"
                   Content="Enregistrer"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="Save_Click"
                   Width="120"/>
        </StackPanel>

        <!-- Indicateur de progression -->
        <Grid x:Name="ProgressOverlay" 
              Grid.RowSpan="3"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"
                           Margin="0,0,0,20"/>
                <TextBlock Text="Enregistrement en cours..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
