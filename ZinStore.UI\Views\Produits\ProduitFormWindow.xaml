<Window x:Class="ZinStore.UI.Views.Produits.ProduitFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Gestion Produit - ZinStore"
        Height="800"
        Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="PackageVariantClosed" 
                                   Width="48" Height="48"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   HorizontalAlignment="Center"/>
            <TextBlock x:Name="TitleText"
                      Text="Nouveau Produit"
                      FontSize="18"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,10,0,0"/>
        </StackPanel>

        <!-- Formulaire -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card Padding="20">
                <StackPanel>
                    <!-- Informations de base -->
                    <TextBlock Text="Informations de base" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Code Produit -->
                    <TextBox x:Name="CodeProduitTextBox"
                            materialDesign:HintAssist.Hint="Code Produit *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Nom -->
                    <TextBox x:Name="NomTextBox"
                            materialDesign:HintAssist.Hint="Nom du Produit *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Description -->
                    <TextBox x:Name="DescriptionTextBox"
                            materialDesign:HintAssist.Hint="Description"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="60"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>

                    <!-- Catégorie et Code Barre -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <ComboBox x:Name="CategorieComboBox"
                                 Grid.Column="0"
                                 materialDesign:HintAssist.Hint="Catégorie *"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 DisplayMemberPath="Nom"
                                 SelectedValuePath="Id"/>
                        
                        <TextBox x:Name="CodeBarreTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Code Barre"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Prix et Unités -->
                    <TextBlock Text="Prix et Unités" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Unité de base -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <ComboBox x:Name="UniteComboBox"
                                 Grid.Column="0"
                                 materialDesign:HintAssist.Hint="Unité de base *"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}">
                            <ComboBoxItem Content="Pièce" IsSelected="True"/>
                            <ComboBoxItem Content="Kg"/>
                            <ComboBoxItem Content="Litre"/>
                            <ComboBoxItem Content="Mètre"/>
                            <ComboBoxItem Content="Boîte"/>
                            <ComboBoxItem Content="Paquet"/>
                        </ComboBox>
                        
                        <TextBox x:Name="PrixAchatTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Prix Achat *"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        
                        <TextBox x:Name="PrixVenteTextBox"
                                Grid.Column="4"
                                materialDesign:HintAssist.Hint="Prix Vente *"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Conditionnement (Carton/Boîte) -->
                    <CheckBox x:Name="AConditionnementCheckBox"
                             Content="Le produit a un conditionnement (carton, boîte, etc.)"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,0,15"
                             Checked="AConditionnement_Checked"
                             Unchecked="AConditionnement_Unchecked"/>

                    <!-- Détails du conditionnement -->
                    <StackPanel x:Name="ConditionnementPanel" 
                               Visibility="Collapsed"
                               Margin="0,0,0,15">
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <ComboBox x:Name="UniteConditionnementComboBox"
                                     Grid.Column="0"
                                     materialDesign:HintAssist.Hint="Unité Conditionnement"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                <ComboBoxItem Content="Carton"/>
                                <ComboBoxItem Content="Boîte"/>
                                <ComboBoxItem Content="Caisse"/>
                                <ComboBoxItem Content="Pack"/>
                                <ComboBoxItem Content="Palette"/>
                            </ComboBox>
                            
                            <TextBox x:Name="QuantiteParConditionnementTextBox"
                                    Grid.Column="2"
                                    materialDesign:HintAssist.Hint="Qté par unité"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    TextChanged="QuantiteParConditionnement_TextChanged"/>
                            
                            <TextBox x:Name="PrixConditionnementTextBox"
                                    Grid.Column="4"
                                    materialDesign:HintAssist.Hint="Prix Conditionnement"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    IsReadOnly="True"
                                    Background="#F5F5F5"/>
                        </Grid>
                        
                        <TextBlock x:Name="ExempleConditionnementTextBlock"
                                  Text="Exemple: 1 Carton = 6 Pièces = 180.00 DA"
                                  FontSize="12"
                                  Foreground="Gray"
                                  FontStyle="Italic"/>
                    </StackPanel>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Stock -->
                    <TextBlock Text="Gestion du Stock" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="StockActuelTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Stock Actuel"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        
                        <TextBox x:Name="StockMinimumTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Stock Minimum"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        
                        <TextBox x:Name="StockMaximumTextBox"
                                Grid.Column="4"
                                materialDesign:HintAssist.Hint="Stock Maximum"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Date de péremption -->
                    <CheckBox x:Name="EstPerissableCheckBox"
                             Content="Produit périssable (avec date d'expiration)"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,0,15"
                             Checked="EstPerissable_Checked"
                             Unchecked="EstPerissable_Unchecked"/>

                    <!-- Détails de péremption -->
                    <StackPanel x:Name="PeremptionPanel" 
                               Visibility="Collapsed"
                               Margin="0,0,0,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="DureeConservationTextBox"
                                    Grid.Column="0"
                                    materialDesign:HintAssist.Hint="Durée de conservation (jours)"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                            
                            <DatePicker x:Name="DateExpirationDatePicker"
                                       Grid.Column="2"
                                       materialDesign:HintAssist.Hint="Date d'expiration"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                        </Grid>
                    </StackPanel>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Autres informations -->
                    <TextBlock Text="Autres Informations" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- TVA et Emplacement -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="TauxTVATextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Taux TVA (%)"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="19"/>
                        
                        <TextBox x:Name="EmplacementTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Emplacement"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Actif -->
                    <CheckBox x:Name="EstActifCheckBox"
                             Content="Produit actif"
                             IsChecked="True"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,10,0,15"/>

                    <!-- Notes -->
                    <TextBox x:Name="NotesTextBox"
                            materialDesign:HintAssist.Hint="Notes"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            <Button Content="Annuler"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click"
                   Width="100"
                   Margin="0,0,10,0"/>
            <Button x:Name="SaveButton"
                   Content="Enregistrer"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="Save_Click"
                   Width="120"/>
        </StackPanel>

        <!-- Indicateur de progression -->
        <Grid x:Name="ProgressOverlay" 
              Grid.RowSpan="3"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"
                           Margin="0,0,0,20"/>
                <TextBlock Text="Enregistrement en cours..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
