<Window x:Class="ZinStore.UI.Views.Clients.ClientFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Gestion Client - ZinStore"
        Height="600"
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="AccountPlus" 
                                   Width="48" Height="48"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   HorizontalAlignment="Center"/>
            <TextBlock x:Name="TitleText"
                      Text="Nouveau Client"
                      FontSize="18"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,10,0,0"/>
        </StackPanel>

        <!-- Formulaire -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card Padding="20">
                <StackPanel>
                    <!-- Code Client -->
                    <TextBox x:Name="CodeClientTextBox"
                            materialDesign:HintAssist.Hint="Code Client *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Nom -->
                    <TextBox x:Name="NomTextBox"
                            materialDesign:HintAssist.Hint="Nom *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Prénom -->
                    <TextBox x:Name="PrenomTextBox"
                            materialDesign:HintAssist.Hint="Prénom *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Téléphone -->
                    <TextBox x:Name="TelephoneTextBox"
                            materialDesign:HintAssist.Hint="Téléphone"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Email -->
                    <TextBox x:Name="EmailTextBox"
                            materialDesign:HintAssist.Hint="Email"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Adresse -->
                    <TextBox x:Name="AdresseTextBox"
                            materialDesign:HintAssist.Hint="Adresse"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="60"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>

                    <!-- Ville -->
                    <TextBox x:Name="VilleTextBox"
                            materialDesign:HintAssist.Hint="Ville"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Code Postal -->
                    <TextBox x:Name="CodePostalTextBox"
                            materialDesign:HintAssist.Hint="Code Postal"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Solde du compte -->
                    <TextBox x:Name="SoldeCompteTextBox"
                            materialDesign:HintAssist.Hint="Solde du Compte (€)"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Limite de crédit -->
                    <TextBox x:Name="LimiteCreditTextBox"
                            materialDesign:HintAssist.Hint="Limite de Crédit (€)"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Actif -->
                    <CheckBox x:Name="EstActifCheckBox"
                             Content="Client actif"
                             IsChecked="True"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,10,0,15"/>

                    <!-- Notes -->
                    <TextBox x:Name="NotesTextBox"
                            materialDesign:HintAssist.Hint="Notes"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            <Button Content="Annuler"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click"
                   Width="100"
                   Margin="0,0,10,0"/>
            <Button x:Name="SaveButton"
                   Content="Enregistrer"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="Save_Click"
                   Width="120"/>
        </StackPanel>

        <!-- Indicateur de progression -->
        <Grid x:Name="ProgressOverlay" 
              Grid.RowSpan="3"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"
                           Margin="0,0,0,20"/>
                <TextBlock Text="Enregistrement en cours..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
