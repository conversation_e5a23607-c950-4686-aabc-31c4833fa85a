using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.Views.Produits
{
    /// <summary>
    /// Logique d'interaction pour ProduitFormWindow.xaml
    /// </summary>
    public partial class ProduitFormWindow : Window
    {
        private readonly ProduitService _produitService;
        private readonly CategorieService _categorieService;
        private Produit _currentProduit;
        private bool _isEditMode;

        public bool ProduitSaved { get; private set; }

        public ProduitFormWindow(Produit produit = null)
        {
            InitializeComponent();
            _produitService = new ProduitService(new DatabaseContext());
            _categorieService = new CategorieService(new DatabaseContext());
            
            if (produit != null)
            {
                _currentProduit = produit;
                _isEditMode = true;
                TitleText.Text = "Modifier Produit";
                LoadProduitData();
            }
            else
            {
                _currentProduit = new Produit();
                _isEditMode = false;
                TitleText.Text = "Nouveau Produit";
                GenerateProduitCode();
            }

            LoadCategories();
        }

        private async void LoadCategories()
        {
            try
            {
                var categories = await _categorieService.GetAllCategoriesAsync();
                CategorieComboBox.ItemsSource = categories;
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des catégories: {ex.Message}");
            }
        }

        private void LoadProduitData()
        {
            try
            {
                CodeProduitTextBox.Text = _currentProduit.CodeProduit;
                NomTextBox.Text = _currentProduit.Nom;
                DescriptionTextBox.Text = _currentProduit.Description;
                CodeBarreTextBox.Text = _currentProduit.CodeBarre;
                UniteComboBox.Text = _currentProduit.Unite;
                PrixAchatTextBox.Text = _currentProduit.PrixAchat.ToString("F2");
                PrixVenteTextBox.Text = _currentProduit.PrixVente.ToString("F2");
                StockActuelTextBox.Text = _currentProduit.StockActuel.ToString("F2");
                StockMinimumTextBox.Text = _currentProduit.StockMinimum.ToString("F2");
                StockMaximumTextBox.Text = _currentProduit.StockMaximum.ToString("F2");
                TauxTVATextBox.Text = _currentProduit.TauxTVA.ToString("F2");
                EmplacementTextBox.Text = _currentProduit.Emplacement;
                EstActifCheckBox.IsChecked = _currentProduit.EstActif;
                EstPerissableCheckBox.IsChecked = _currentProduit.EstPerissable;
                DureeConservationTextBox.Text = _currentProduit.DureeConservation.ToString();
                NotesTextBox.Text = _currentProduit.Notes;

                // Sélectionner la catégorie
                CategorieComboBox.SelectedValue = _currentProduit.CategorieId;

                // Afficher les panneaux si nécessaire
                if (_currentProduit.EstPerissable)
                {
                    PeremptionPanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
        }

        private void GenerateProduitCode()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                CodeProduitTextBox.Text = $"PR{timestamp.Substring(8)}";
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la génération du code: {ex.Message}");
            }
        }

        private void AConditionnement_Checked(object sender, RoutedEventArgs e)
        {
            ConditionnementPanel.Visibility = Visibility.Visible;
            CalculerPrixConditionnement();
        }

        private void AConditionnement_Unchecked(object sender, RoutedEventArgs e)
        {
            ConditionnementPanel.Visibility = Visibility.Collapsed;
            // Réinitialiser les valeurs
            UniteConditionnementComboBox.SelectedIndex = -1;
            QuantiteParConditionnementTextBox.Text = "";
            PrixConditionnementTextBox.Text = "";
            ExempleConditionnementTextBlock.Text = "";
        }

        private void EstPerissable_Checked(object sender, RoutedEventArgs e)
        {
            PeremptionPanel.Visibility = Visibility.Visible;
        }

        private void EstPerissable_Unchecked(object sender, RoutedEventArgs e)
        {
            PeremptionPanel.Visibility = Visibility.Collapsed;
            DureeConservationTextBox.Text = "0";
            DateExpirationDatePicker.SelectedDate = null;
        }

        private void QuantiteParConditionnement_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculerPrixConditionnement();
        }

        private void CalculerPrixConditionnement()
        {
            try
            {
                if (decimal.TryParse(PrixVenteTextBox.Text, out decimal prixVente) &&
                    decimal.TryParse(QuantiteParConditionnementTextBox.Text, out decimal quantite) &&
                    quantite > 0)
                {
                    decimal prixConditionnement = prixVente * quantite;
                    PrixConditionnementTextBox.Text = prixConditionnement.ToString("F2");

                    // Mettre à jour l'exemple
                    string uniteBase = UniteComboBox.Text ?? "Pièce";
                    string uniteConditionnement = (UniteConditionnementComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "Conditionnement";
                    
                    ExempleConditionnementTextBlock.Text = 
                        $"Exemple: 1 {uniteConditionnement} = {quantite} {uniteBase} = {prixConditionnement:F2} DA";
                }
                else
                {
                    PrixConditionnementTextBox.Text = "";
                    ExempleConditionnementTextBlock.Text = "";
                }
            }
            catch
            {
                // Ignorer les erreurs de calcul
            }
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                ShowProgress();

                // Remplir l'objet produit avec les données du formulaire
                _currentProduit.CodeProduit = CodeProduitTextBox.Text.Trim();
                _currentProduit.Nom = NomTextBox.Text.Trim();
                _currentProduit.Description = DescriptionTextBox.Text.Trim();
                _currentProduit.CodeBarre = CodeBarreTextBox.Text.Trim();
                _currentProduit.CategorieId = (int)CategorieComboBox.SelectedValue;
                _currentProduit.Unite = UniteComboBox.Text;
                _currentProduit.EstActif = EstActifCheckBox.IsChecked ?? true;
                _currentProduit.EstPerissable = EstPerissableCheckBox.IsChecked ?? false;
                _currentProduit.Emplacement = EmplacementTextBox.Text.Trim();
                _currentProduit.Notes = NotesTextBox.Text.Trim();

                // Convertir les valeurs numériques
                if (decimal.TryParse(PrixAchatTextBox.Text, out decimal prixAchat))
                    _currentProduit.PrixAchat = prixAchat;

                if (decimal.TryParse(PrixVenteTextBox.Text, out decimal prixVente))
                    _currentProduit.PrixVente = prixVente;

                if (decimal.TryParse(StockActuelTextBox.Text, out decimal stockActuel))
                    _currentProduit.StockActuel = stockActuel;

                if (decimal.TryParse(StockMinimumTextBox.Text, out decimal stockMinimum))
                    _currentProduit.StockMinimum = stockMinimum;

                if (decimal.TryParse(StockMaximumTextBox.Text, out decimal stockMaximum))
                    _currentProduit.StockMaximum = stockMaximum;

                if (decimal.TryParse(TauxTVATextBox.Text, out decimal tauxTVA))
                    _currentProduit.TauxTVA = tauxTVA;

                if (int.TryParse(DureeConservationTextBox.Text, out int dureeConservation))
                    _currentProduit.DureeConservation = dureeConservation;

                // Sauvegarder
                bool success;
                string message;
                if (_isEditMode)
                {
                    var result = await _produitService.UpdateProduitAsync(_currentProduit);
                    success = result.Success;
                    message = result.Message;
                }
                else
                {
                    _currentProduit.DateCreation = DateTime.Now;
                    var result = await _produitService.AddProduitAsync(_currentProduit);
                    success = result.Success;
                    message = result.Message;
                }

                HideProgress();

                if (success)
                {
                    ProduitSaved = true;
                    MessageBoxHelper.ShowSuccess(message);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBoxHelper.ShowError(message);
                }
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBoxHelper.ShowError($"Erreur lors de l'enregistrement: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // Vérifier les champs obligatoires
            if (string.IsNullOrWhiteSpace(CodeProduitTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le code produit est obligatoire.");
                CodeProduitTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NomTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le nom du produit est obligatoire.");
                NomTextBox.Focus();
                return false;
            }

            if (CategorieComboBox.SelectedValue == null)
            {
                MessageBoxHelper.ShowWarning("La catégorie est obligatoire.");
                CategorieComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(PrixAchatTextBox.Text) || !decimal.TryParse(PrixAchatTextBox.Text, out _))
            {
                MessageBoxHelper.ShowWarning("Le prix d'achat doit être un nombre valide.");
                PrixAchatTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(PrixVenteTextBox.Text) || !decimal.TryParse(PrixVenteTextBox.Text, out _))
            {
                MessageBoxHelper.ShowWarning("Le prix de vente doit être un nombre valide.");
                PrixVenteTextBox.Focus();
                return false;
            }

            return true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowProgress()
        {
            ProgressOverlay.Visibility = Visibility.Visible;
            SaveButton.IsEnabled = false;
        }

        private void HideProgress()
        {
            ProgressOverlay.Visibility = Visibility.Collapsed;
            SaveButton.IsEnabled = true;
        }
    }
}
