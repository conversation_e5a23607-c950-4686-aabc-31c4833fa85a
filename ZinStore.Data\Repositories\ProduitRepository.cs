using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class ProduitRepository : BaseRepository<Produit>
    {
        public ProduitRepository(DatabaseContext context) : base(context, "Produits")
        {
        }

        public override async Task<IEnumerable<Produit>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT p.*, c.Nom as CategorieName, f.Nom as FournisseurName
                FROM Produits p
                LEFT JOIN Categories c ON p.CategorieId = c.Id
                LEFT JOIN Fournisseurs f ON p.FournisseurId = f.Id
                WHERE p.EstSupprime = 0 
                AND (p.CodeProduit LIKE @SearchTerm OR p.Nom LIKE @SearchTerm OR p.CodeBarre LIKE @SearchTerm)
                ORDER BY p.Nom";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Produit>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<IEnumerable<Produit>> GetActiveProduitsAsync()
        {
            var sql = @"
                SELECT p.*, c.Nom as CategorieName, f.Nom as FournisseurName
                FROM Produits p
                LEFT JOIN Categories c ON p.CategorieId = c.Id
                LEFT JOIN Fournisseurs f ON p.FournisseurId = f.Id
                WHERE p.EstActif = 1 AND p.EstSupprime = 0 
                ORDER BY p.Nom";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Produit>(sql);
            }
        }

        public async Task<IEnumerable<Produit>> GetProduitsEnRuptureAsync()
        {
            var sql = @"
                SELECT p.*, c.Nom as CategorieName
                FROM Produits p
                LEFT JOIN Categories c ON p.CategorieId = c.Id
                WHERE p.StockActuel <= p.StockMinimum 
                AND p.EstActif = 1 AND p.EstSupprime = 0 
                ORDER BY p.StockActuel";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Produit>(sql);
            }
        }

        public async Task<string> GenerateNextCodeAsync(string prefix = "PR")
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTR(CodeProduit, LENGTH(@Prefix) + 1) AS INTEGER)), 0) + 1 
                FROM Produits WHERE CodeProduit LIKE @Pattern AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                var nextNumber = await connection.QuerySingleAsync<int>(sql, 
                    new { Prefix = prefix, Pattern = $"{prefix}%" });
                return $"{prefix}{nextNumber:D6}";
            }
        }
    }
}
