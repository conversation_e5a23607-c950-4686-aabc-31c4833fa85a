<Window x:Class="ZinStore.UI.Views.Fournisseurs.FournisseurFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Gestion Fournisseur - ZinStore"
        Height="650"
        Width="550"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="TruckDelivery" 
                                   Width="48" Height="48"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   HorizontalAlignment="Center"/>
            <TextBlock x:Name="TitleText"
                      Text="Nouveau Fournisseur"
                      FontSize="18"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,10,0,0"/>
        </StackPanel>

        <!-- Formulaire -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card Padding="20">
                <StackPanel>
                    <!-- Informations de base -->
                    <TextBlock Text="Informations de base" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Code Fournisseur -->
                    <TextBox x:Name="CodeFournisseurTextBox"
                            materialDesign:HintAssist.Hint="Code Fournisseur *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Raison Sociale -->
                    <TextBox x:Name="RaisonSocialeTextBox"
                            materialDesign:HintAssist.Hint="Raison Sociale *"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Nom Commercial -->
                    <TextBox x:Name="NomCommercialTextBox"
                            materialDesign:HintAssist.Hint="Nom Commercial"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,15"/>

                    <!-- Contact -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="PersonneContactTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Personne de Contact"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        
                        <TextBox x:Name="TelephoneTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Téléphone"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Email et Fax -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="EmailTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Email"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        
                        <TextBox x:Name="FaxTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Fax"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Adresse -->
                    <TextBlock Text="Adresse" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Adresse complète -->
                    <TextBox x:Name="AdresseTextBox"
                            materialDesign:HintAssist.Hint="Adresse"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="60"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>

                    <!-- Ville et Code Postal -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="VilleTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Ville"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        
                        <TextBox x:Name="CodePostalTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Code Postal"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Pays -->
                    <TextBox x:Name="PaysTextBox"
                            materialDesign:HintAssist.Hint="Pays"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Text="Algérie"
                            Margin="0,0,0,15"/>

                    <!-- Séparateur -->
                    <Separator Margin="0,10,0,20"/>

                    <!-- Informations commerciales -->
                    <TextBlock Text="Informations Commerciales" 
                              FontWeight="Bold" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Conditions de paiement -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <ComboBox x:Name="ConditionsPaiementComboBox"
                                 Grid.Column="0"
                                 materialDesign:HintAssist.Hint="Conditions de Paiement"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}">
                            <ComboBoxItem Content="Comptant"/>
                            <ComboBoxItem Content="30 jours"/>
                            <ComboBoxItem Content="60 jours"/>
                            <ComboBoxItem Content="90 jours"/>
                            <ComboBoxItem Content="À la livraison"/>
                        </ComboBox>
                        
                        <TextBox x:Name="DelaiLivraisonTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Délai Livraison (jours)"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Solde et Limite -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="SoldeCompteTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Solde du Compte (€)"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="0.00"/>
                        
                        <TextBox x:Name="LimiteCreditTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Limite de Crédit (€)"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="0.00"/>
                    </Grid>

                    <!-- Informations fiscales -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="NumeroTVATextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Numéro TVA"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        
                        <TextBox x:Name="RegistreCommerceTextBox"
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="Registre de Commerce"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </Grid>

                    <!-- Actif -->
                    <CheckBox x:Name="EstActifCheckBox"
                             Content="Fournisseur actif"
                             IsChecked="True"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,10,0,15"/>

                    <!-- Notes -->
                    <TextBox x:Name="NotesTextBox"
                            materialDesign:HintAssist.Hint="Notes"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,15"/>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            <Button Content="Annuler"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click"
                   Width="100"
                   Margin="0,0,10,0"/>
            <Button x:Name="SaveButton"
                   Content="Enregistrer"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="Save_Click"
                   Width="120"/>
        </StackPanel>

        <!-- Indicateur de progression -->
        <Grid x:Name="ProgressOverlay" 
              Grid.RowSpan="3"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"
                           Margin="0,0,0,20"/>
                <TextBlock Text="Enregistrement en cours..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
