using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Interfaces;

namespace ZinStore.Data.Repositories
{
    /// <summary>
    /// Repository de base pour toutes les entités
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public abstract class BaseRepository<T> : IRepository<T> where T : BaseEntity
    {
        protected readonly DatabaseContext _context;
        protected readonly string _tableName;

        protected BaseRepository(DatabaseContext context, string tableName)
        {
            _context = context;
            _tableName = tableName;
        }

        /// <summary>
        /// Obtient toutes les entités non supprimées
        /// </summary>
        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using (var connection = _context.GetConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE EstSupprime = 0 ORDER BY Id";
                return await connection.QueryAsync<T>(sql);
            }
        }

        /// <summary>
        /// Obtient une entité par son ID
        /// </summary>
        public virtual async Task<T> GetByIdAsync(int id)
        {
            using (var connection = _context.GetConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE Id = @Id AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
            }
        }

        /// <summary>
        /// Ajoute une nouvelle entité
        /// </summary>
        public virtual async Task<int> AddAsync(T entity)
        {
            entity.DateCreation = DateTime.Now;
            entity.EstSupprime = false;

            var properties = GetProperties(entity, excludeKey: true);
            var columns = string.Join(", ", properties.Select(p => p.Name));
            var values = string.Join(", ", properties.Select(p => "@" + p.Name));

            var sql = $"INSERT INTO {_tableName} ({columns}) VALUES ({values}); SELECT last_insert_rowid();";

            using (var connection = _context.GetConnection())
            {
                var id = await connection.QuerySingleAsync<int>(sql, entity);
                entity.Id = id;
                return id;
            }
        }

        /// <summary>
        /// Met à jour une entité existante
        /// </summary>
        public virtual async Task<bool> UpdateAsync(T entity)
        {
            entity.DateModification = DateTime.Now;

            var properties = GetProperties(entity, excludeKey: true);
            var setClause = string.Join(", ", properties.Select(p => $"{p.Name} = @{p.Name}"));

            var sql = $"UPDATE {_tableName} SET {setClause} WHERE Id = @Id";

            using (var connection = _context.GetConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, entity);
                return affectedRows > 0;
            }
        }

        /// <summary>
        /// Supprime une entité (suppression logique)
        /// </summary>
        public virtual async Task<bool> DeleteAsync(int id)
        {
            var sql = $"UPDATE {_tableName} SET EstSupprime = 1, DateModification = @DateModification WHERE Id = @Id";

            using (var connection = _context.GetConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, new { Id = id, DateModification = DateTime.Now });
                return affectedRows > 0;
            }
        }

        /// <summary>
        /// Supprime définitivement une entité
        /// </summary>
        public virtual async Task<bool> HardDeleteAsync(int id)
        {
            var sql = $"DELETE FROM {_tableName} WHERE Id = @Id";

            using (var connection = _context.GetConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, new { Id = id });
                return affectedRows > 0;
            }
        }

        /// <summary>
        /// Obtient les entités avec pagination
        /// </summary>
        public virtual async Task<IEnumerable<T>> GetPagedAsync(int pageNumber, int pageSize)
        {
            var offset = (pageNumber - 1) * pageSize;
            var sql = $"SELECT * FROM {_tableName} WHERE EstSupprime = 0 ORDER BY Id LIMIT @PageSize OFFSET @Offset";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<T>(sql, new { PageSize = pageSize, Offset = offset });
            }
        }

        /// <summary>
        /// Compte le nombre total d'entités non supprimées
        /// </summary>
        public virtual async Task<int> CountAsync()
        {
            var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                return await connection.QuerySingleAsync<int>(sql);
            }
        }

        /// <summary>
        /// Recherche des entités selon un critère (à implémenter dans les classes dérivées)
        /// </summary>
        public abstract Task<IEnumerable<T>> SearchAsync(string searchTerm);

        /// <summary>
        /// Vérifie si une entité existe
        /// </summary>
        public virtual async Task<bool> ExistsAsync(int id)
        {
            var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE Id = @Id AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                var count = await connection.QuerySingleAsync<int>(sql, new { Id = id });
                return count > 0;
            }
        }

        /// <summary>
        /// Obtient les propriétés d'une entité pour les requêtes SQL
        /// </summary>
        protected IEnumerable<PropertyInfo> GetProperties(T entity, bool excludeKey = false)
        {
            var properties = typeof(T).GetProperties()
                .Where(p => p.CanRead && p.CanWrite);

            if (excludeKey)
            {
                properties = properties.Where(p => p.Name != "Id");
            }

            return properties;
        }
    }
}
