using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class VentesViewModel : BaseViewModel
    {
        private readonly VenteService _venteService;

        public VentesViewModel()
        {
            _venteService = new VenteService(new DatabaseContext());

            Ventes = new ObservableCollection<Vente>();

            AddVenteCommand = new RelayCommand(AddVente);
            ViewDetailsCommand = new RelayCommand(ViewDetails, CanViewDetails);
            PrintCommand = new RelayCommand(Print, CanPrint);
            RefreshCommand = new RelayCommand(async () => await LoadVentesAsync());

            // Initialiser les dates par défaut
            DateDebut = DateTime.Today.AddDays(-30);
            DateFin = DateTime.Today;

            _ = LoadVentesAsync();
        }

        public ObservableCollection<Vente> Ventes { get; }

        private Vente _selectedVente;
        public Vente SelectedVente
        {
            get => _selectedVente;
            set => SetProperty(ref _selectedVente, value);
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                SearchVentes();
            }
        }

        private DateTime? _dateDebut;
        public DateTime? DateDebut
        {
            get => _dateDebut;
            set
            {
                SetProperty(ref _dateDebut, value);
                FilterVentes();
            }
        }

        private DateTime? _dateFin;
        public DateTime? DateFin
        {
            get => _dateFin;
            set
            {
                SetProperty(ref _dateFin, value);
                FilterVentes();
            }
        }

        public ICommand AddVenteCommand { get; }
        public ICommand ViewDetailsCommand { get; }
        public ICommand PrintCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadVentesAsync()
        {
            try
            {
                IsBusy = true;
                var ventes = await _venteService.GetAllVentesAsync();

                Ventes.Clear();
                foreach (var vente in ventes)
                {
                    Ventes.Add(vente);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des ventes: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void SearchVentes()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadVentesAsync();
                return;
            }

            try
            {
                // Pour l'instant, filtrer localement
                // En production, implémenter une recherche côté service
                var allVentes = await _venteService.GetAllVentesAsync();
                var filteredVentes = allVentes.Where(v =>
                    v.NumeroFacture.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    v.NomClient.Contains(SearchText, StringComparison.OrdinalIgnoreCase));

                Ventes.Clear();
                foreach (var vente in filteredVentes)
                {
                    Ventes.Add(vente);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private async void FilterVentes()
        {
            try
            {
                var allVentes = await _venteService.GetAllVentesAsync();
                var filteredVentes = allVentes.AsEnumerable();

                if (DateDebut.HasValue)
                {
                    filteredVentes = filteredVentes.Where(v => v.DateVente >= DateDebut.Value);
                }

                if (DateFin.HasValue)
                {
                    filteredVentes = filteredVentes.Where(v => v.DateVente <= DateFin.Value.AddDays(1));
                }

                Ventes.Clear();
                foreach (var vente in filteredVentes)
                {
                    Ventes.Add(vente);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du filtrage: {ex.Message}");
            }
        }

        private void AddVente()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité de nouvelle vente en cours de développement.");
        }

        private void ViewDetails()
        {
            if (SelectedVente == null) return;
            MessageBoxHelper.ShowInfo($"Détails de la vente {SelectedVente.NumeroFacture} en cours de développement.");
        }

        private void Print()
        {
            if (SelectedVente == null) return;
            MessageBoxHelper.ShowInfo($"Impression de la facture {SelectedVente.NumeroFacture} en cours de développement.");
        }

        private bool CanViewDetails()
        {
            return SelectedVente != null;
        }

        private bool CanPrint()
        {
            return SelectedVente != null;
        }
    }
}
