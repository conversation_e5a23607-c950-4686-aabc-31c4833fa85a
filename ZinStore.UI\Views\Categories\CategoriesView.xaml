<UserControl x:Class="ZinStore.UI.Views.Categories.CategoriesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.DataContext>
        <viewModels:CategoriesViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="FolderMultiple" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="Gestion des Catégories"
                              FontSize="20"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddCategorieCommand}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" VerticalAlignment="Center"/>
                            <TextBlock Text="Nouvelle Catégorie" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" VerticalAlignment="Center"/>
                            <TextBlock Text="Actualiser" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Barre de recherche et filtres -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <TextBox Grid.Column="0"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        materialDesign:HintAssist.Hint="Rechercher une catégorie..."
                        Style="{StaticResource MaterialDesignOutlinedTextBox}">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Filtre statut -->
                <ComboBox Grid.Column="2"
                         SelectedValue="{Binding FiltreStatut}"
                         materialDesign:HintAssist.Hint="Statut"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}">
                    <ComboBoxItem Content="Toutes" Tag="Toutes"/>
                    <ComboBoxItem Content="Actives" Tag="Actives"/>
                    <ComboBoxItem Content="Inactives" Tag="Inactives"/>
                </ComboBox>

                <!-- Bouton recherche -->
                <Button Grid.Column="4"
                       Style="{StaticResource MaterialDesignIconButton}"
                       Command="{Binding SearchCommand}"
                       ToolTip="Rechercher">
                    <materialDesign:PackIcon Kind="Magnify"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des catégories -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- En-tête du tableau -->
                <Border Grid.Row="0" 
                       Background="{DynamicResource PrimaryHueLightBrush}" 
                       Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Code" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="1" Text="Nom" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="2" Text="Description" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="3" Text="Nb Produits" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="4" Text="Statut" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="5" Text="Actions" FontWeight="Bold" Foreground="White"/>
                    </Grid>
                </Border>

                <!-- Liste -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding Categories}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="{DynamicResource MaterialDesignDivider}" 
                                       BorderThickness="0,0,0,1"
                                       Padding="20,15">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="150"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" 
                                                  Text="{Binding CodeCategorie}" 
                                                  VerticalAlignment="Center"
                                                  FontWeight="Bold"/>

                                        <TextBlock Grid.Column="1" 
                                                  Text="{Binding Nom}" 
                                                  VerticalAlignment="Center"
                                                  FontWeight="Medium"/>

                                        <TextBlock Grid.Column="2" 
                                                  Text="{Binding Description}" 
                                                  VerticalAlignment="Center"
                                                  TextTrimming="CharacterEllipsis"/>

                                        <TextBlock Grid.Column="3" 
                                                  Text="{Binding NombreProduits, FallbackValue=0}" 
                                                  VerticalAlignment="Center"
                                                  HorizontalAlignment="Center"/>

                                        <materialDesign:Chip Grid.Column="4"
                                                           Content="{Binding EstActif, Converter={StaticResource BoolToStatusConverter}}"
                                                           Background="{Binding EstActif, Converter={StaticResource BoolToColorConverter}}"
                                                           Foreground="White"
                                                           FontSize="10"
                                                           Height="25"
                                                           VerticalAlignment="Center"/>

                                        <StackPanel Grid.Column="5" 
                                                   Orientation="Horizontal" 
                                                   VerticalAlignment="Center">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.EditCategorieCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Modifier">
                                                <materialDesign:PackIcon Kind="Edit" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            </Button>
                                            
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.DeleteCategorieCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Supprimer">
                                                <materialDesign:PackIcon Kind="Delete" Foreground="Red"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- Indicateur de chargement -->
                <Grid Grid.Row="1" 
                     Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                   IsIndeterminate="True"
                                   Width="50" Height="50"
                                   Margin="0,0,0,20"/>
                        <TextBlock Text="Chargement des catégories..."
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- Message vide -->
                <StackPanel Grid.Row="1"
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           Visibility="{Binding IsEmpty, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="FolderOpen" 
                                           Width="64" Height="64"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="Aucune catégorie trouvée"
                              FontSize="16"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"/>
                </StackPanel>

                <!-- Pied de page -->
                <Border Grid.Row="2" 
                       Background="{DynamicResource MaterialDesignCardBackground}" 
                       Padding="20,10"
                       BorderBrush="{DynamicResource MaterialDesignDivider}"
                       BorderThickness="0,1,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0"
                                   Orientation="Horizontal"
                                   VerticalAlignment="Center">
                            <TextBlock Text="Total: "/>
                            <TextBlock Text="{Binding Categories.Count}" FontWeight="Bold"/>
                            <TextBlock Text=" catégorie(s)"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="Dernière mise à jour: " VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding LastUpdateTime, StringFormat='dd/MM/yyyy HH:mm'}" 
                                      FontWeight="Bold" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
