using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Core.Models;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class SettingsViewModel : BaseViewModel
    {
        private string _companyName = "ZinStore";
        private string _companyAddress;
        private string _companyPhone;
        private string _companyEmail;
        private string _companyRC;
        private string _companyTaxNumber;
        private decimal _defaultTaxRate = 19;
        private string _defaultCurrency = "DA";
        private bool _autoPrintInvoices = false;
        private bool _confirmBeforeDelete = true;
        private bool _lowStockAlerts = true;
        private string _selectedTheme = "Light";
        private string _selectedLanguage = "fr";
        private int _itemsPerPage = 50;
        private bool _autoBackup = true;
        private string _backupPath;
        private string _appVersion = "1.0.0";
        private string _databaseStatus = "Connectée";
        private DateTime? _lastBackupDate;
        private string _diskSpace = "Calcul en cours...";

        public SettingsViewModel()
        {
            CashRegisters = new ObservableCollection<CashRegister>();
            
            SaveSettingsCommand = new RelayCommand(async () => await SaveSettingsAsync());
            AddCashRegisterCommand = new RelayCommand(AddCashRegister);
            EditCashRegisterCommand = new RelayCommand(param => EditCashRegister(param as CashRegister));
            DeleteCashRegisterCommand = new RelayCommand(param => DeleteCashRegister(param as CashRegister));
            CreateBackupCommand = new RelayCommand(async () => await CreateBackupAsync());
            RestoreBackupCommand = new RelayCommand(async () => await RestoreBackupAsync());

            LoadSettings();
            LoadCashRegisters();
            UpdateSystemInfo();
        }

        // Propriétés de l'entreprise
        public string CompanyName
        {
            get => _companyName;
            set => SetProperty(ref _companyName, value);
        }

        public string CompanyAddress
        {
            get => _companyAddress;
            set => SetProperty(ref _companyAddress, value);
        }

        public string CompanyPhone
        {
            get => _companyPhone;
            set => SetProperty(ref _companyPhone, value);
        }

        public string CompanyEmail
        {
            get => _companyEmail;
            set => SetProperty(ref _companyEmail, value);
        }

        public string CompanyRC
        {
            get => _companyRC;
            set => SetProperty(ref _companyRC, value);
        }

        public string CompanyTaxNumber
        {
            get => _companyTaxNumber;
            set => SetProperty(ref _companyTaxNumber, value);
        }

        // Paramètres de vente
        public decimal DefaultTaxRate
        {
            get => _defaultTaxRate;
            set => SetProperty(ref _defaultTaxRate, value);
        }

        public string DefaultCurrency
        {
            get => _defaultCurrency;
            set => SetProperty(ref _defaultCurrency, value);
        }

        public bool AutoPrintInvoices
        {
            get => _autoPrintInvoices;
            set => SetProperty(ref _autoPrintInvoices, value);
        }

        public bool ConfirmBeforeDelete
        {
            get => _confirmBeforeDelete;
            set => SetProperty(ref _confirmBeforeDelete, value);
        }

        public bool LowStockAlerts
        {
            get => _lowStockAlerts;
            set => SetProperty(ref _lowStockAlerts, value);
        }

        // Paramètres d'affichage
        public string SelectedTheme
        {
            get => _selectedTheme;
            set => SetProperty(ref _selectedTheme, value);
        }

        public string SelectedLanguage
        {
            get => _selectedLanguage;
            set => SetProperty(ref _selectedLanguage, value);
        }

        public int ItemsPerPage
        {
            get => _itemsPerPage;
            set => SetProperty(ref _itemsPerPage, value);
        }

        // Sauvegarde
        public bool AutoBackup
        {
            get => _autoBackup;
            set => SetProperty(ref _autoBackup, value);
        }

        public string BackupPath
        {
            get => _backupPath;
            set => SetProperty(ref _backupPath, value);
        }

        // Informations système
        public string AppVersion
        {
            get => _appVersion;
            set => SetProperty(ref _appVersion, value);
        }

        public string DatabaseStatus
        {
            get => _databaseStatus;
            set => SetProperty(ref _databaseStatus, value);
        }

        public DateTime? LastBackupDate
        {
            get => _lastBackupDate;
            set => SetProperty(ref _lastBackupDate, value);
        }

        public string DiskSpace
        {
            get => _diskSpace;
            set => SetProperty(ref _diskSpace, value);
        }

        // Collections
        public ObservableCollection<CashRegister> CashRegisters { get; }

        // Commandes
        public ICommand SaveSettingsCommand { get; }
        public ICommand AddCashRegisterCommand { get; }
        public ICommand EditCashRegisterCommand { get; }
        public ICommand DeleteCashRegisterCommand { get; }
        public ICommand CreateBackupCommand { get; }
        public ICommand RestoreBackupCommand { get; }

        private void LoadSettings()
        {
            try
            {
                // Charger les paramètres depuis la configuration
                // TODO: Implémenter le chargement depuis la base de données ou fichier config
                BackupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "ZinStore", "Backups");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des paramètres: {ex.Message}");
            }
        }

        private void LoadCashRegisters()
        {
            try
            {
                // Charger les caisses depuis la base de données
                // TODO: Implémenter le chargement depuis la base de données
                CashRegisters.Add(new CashRegister
                {
                    Id = 1,
                    Name = "Caisse Principale",
                    InitialBalance = 1000,
                    CurrentBalance = 1250.50m,
                    IsActive = true
                });

                CashRegisters.Add(new CashRegister
                {
                    Id = 2,
                    Name = "Caisse Secondaire",
                    InitialBalance = 500,
                    CurrentBalance = 750.25m,
                    IsActive = false
                });
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des caisses: {ex.Message}");
            }
        }

        private void UpdateSystemInfo()
        {
            try
            {
                // Calculer l'espace disque
                var drive = new DriveInfo(Path.GetPathRoot(Environment.CurrentDirectory));
                var freeSpace = drive.AvailableFreeSpace / (1024 * 1024 * 1024); // GB
                var totalSpace = drive.TotalSize / (1024 * 1024 * 1024); // GB
                DiskSpace = $"{freeSpace:F1} GB libre sur {totalSpace:F1} GB";

                // Dernière sauvegarde
                LastBackupDate = DateTime.Now.AddDays(-2); // Exemple
            }
            catch (Exception ex)
            {
                DiskSpace = "Impossible de calculer";
                MessageBoxHelper.ShowError($"Erreur lors de la mise à jour des informations système: {ex.Message}");
            }
        }

        private async Task SaveSettingsAsync()
        {
            try
            {
                IsBusy = true;
                
                // TODO: Sauvegarder les paramètres dans la base de données
                await Task.Delay(1000); // Simulation
                
                MessageBoxHelper.ShowSuccess("Paramètres sauvegardés avec succès.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la sauvegarde: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddCashRegister()
        {
            var window = new Views.Settings.CashRegisterFormWindow();
            if (window.ShowDialog() == true && window.CashRegisterSaved)
            {
                LoadCashRegisters();
            }
        }

        private void EditCashRegister(CashRegister cashRegister)
        {
            if (cashRegister == null) return;
            
            var window = new Views.Settings.CashRegisterFormWindow(cashRegister);
            if (window.ShowDialog() == true && window.CashRegisterSaved)
            {
                LoadCashRegisters();
            }
        }

        private void DeleteCashRegister(CashRegister cashRegister)
        {
            if (cashRegister == null) return;
            
            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer la caisse '{cashRegister.Name}' ?"))
            {
                try
                {
                    // TODO: Supprimer de la base de données
                    CashRegisters.Remove(cashRegister);
                    MessageBoxHelper.ShowSuccess("Caisse supprimée avec succès.");
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
                }
            }
        }

        private async Task CreateBackupAsync()
        {
            try
            {
                IsBusy = true;
                
                // TODO: Implémenter la création de sauvegarde
                await Task.Delay(2000); // Simulation
                
                LastBackupDate = DateTime.Now;
                MessageBoxHelper.ShowSuccess("Sauvegarde créée avec succès.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la création de la sauvegarde: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task RestoreBackupAsync()
        {
            try
            {
                if (MessageBoxHelper.ShowConfirmation("Êtes-vous sûr de vouloir restaurer depuis une sauvegarde ? Cette opération remplacera toutes les données actuelles."))
                {
                    IsBusy = true;
                    
                    // TODO: Implémenter la restauration
                    await Task.Delay(3000); // Simulation
                    
                    MessageBoxHelper.ShowSuccess("Restauration effectuée avec succès.");
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la restauration: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }

    // Modèle pour les caisses
    public class CashRegister
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal InitialBalance { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; } = DateTime.Now;
        public string Description { get; set; }
    }
}
