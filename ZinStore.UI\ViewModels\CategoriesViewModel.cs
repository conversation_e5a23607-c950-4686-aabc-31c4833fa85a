using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class CategoriesViewModel : BaseViewModel
    {
        private readonly CategorieService _categorieService;
        private string _searchText;
        private string _filtreStatut = "Toutes";
        private Categorie _selectedCategorie;
        private DateTime _lastUpdateTime;

        public CategoriesViewModel()
        {
            _categorieService = new CategorieService(new DatabaseContext());

            Categories = new ObservableCollection<Categorie>();

            AddCategorieCommand = new RelayCommand(AddCategorie);
            EditCategorieCommand = new RelayCommand<Categorie>(EditCategorie, CanEditCategorie);
            DeleteCategorieCommand = new RelayCommand<Categorie>(DeleteCategorie, CanDeleteCategorie);
            SearchCommand = new RelayCommand(async () => await SearchCategoriesAsync());
            RefreshCommand = new RelayCommand(async () => await LoadCategoriesAsync());

            _ = LoadCategoriesAsync();
        }

        public ObservableCollection<Categorie> Categories { get; }

        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                if (string.IsNullOrWhiteSpace(value))
                {
                    _ = LoadCategoriesAsync();
                }
            }
        }

        public string FiltreStatut
        {
            get => _filtreStatut;
            set
            {
                SetProperty(ref _filtreStatut, value);
                FilterCategories();
            }
        }

        public Categorie SelectedCategorie
        {
            get => _selectedCategorie;
            set => SetProperty(ref _selectedCategorie, value);
        }

        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set => SetProperty(ref _lastUpdateTime, value);
        }

        public bool IsEmpty => !IsBusy && Categories.Count == 0;

        public ICommand AddCategorieCommand { get; }
        public ICommand EditCategorieCommand { get; }
        public ICommand DeleteCategorieCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                IsBusy = true;
                var categories = await _categorieService.GetAllCategoriesAsync();

                Categories.Clear();
                foreach (var categorie in categories)
                {
                    Categories.Add(categorie);
                }

                LastUpdateTime = DateTime.Now;
                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des catégories: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SearchCategoriesAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadCategoriesAsync();
                return;
            }

            try
            {
                IsBusy = true;
                var categories = await _categorieService.SearchCategoriesAsync(SearchText);

                Categories.Clear();
                foreach (var categorie in categories)
                {
                    Categories.Add(categorie);
                }

                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void FilterCategories()
        {
            // Cette méthode peut être étendue pour filtrer localement
            // Pour l'instant, on recharge depuis la base
            _ = LoadCategoriesAsync();
        }

        private void AddCategorie()
        {
            var window = new Views.Categories.CategorieFormWindow();
            if (window.ShowDialog() == true && window.CategorieSaved)
            {
                _ = LoadCategoriesAsync();
            }
        }

        private void EditCategorie(Categorie categorie)
        {
            if (categorie == null) return;
            
            var window = new Views.Categories.CategorieFormWindow(categorie);
            if (window.ShowDialog() == true && window.CategorieSaved)
            {
                _ = LoadCategoriesAsync();
            }
        }

        private async void DeleteCategorie(Categorie categorie)
        {
            if (categorie == null) return;
            
            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer la catégorie '{categorie.Nom}' ?"))
            {
                try
                {
                    var result = await _categorieService.DeleteCategorieAsync(categorie.Id);
                    if (result.Success)
                    {
                        MessageBoxHelper.ShowSuccess(result.Message);
                        await LoadCategoriesAsync();
                    }
                    else
                    {
                        MessageBoxHelper.ShowError(result.Message);
                    }
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
                }
            }
        }

        private bool CanEditCategorie(Categorie categorie)
        {
            return categorie != null;
        }

        private bool CanDeleteCategorie(Categorie categorie)
        {
            return categorie != null;
        }
    }
}
