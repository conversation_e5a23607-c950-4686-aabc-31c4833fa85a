using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class ProduitsViewModel : BaseViewModel
    {
        private readonly ProduitService _produitService;

        public ProduitsViewModel()
        {
            _produitService = new ProduitService(new DatabaseContext());

            Produits = new ObservableCollection<Produit>();

            AddProduitCommand = new RelayCommand(AddProduit);
            EditProduitCommand = new RelayCommand(EditProduit, CanEditProduit);
            DeleteProduitCommand = new RelayCommand(DeleteProduit, CanDeleteProduit);
            RefreshCommand = new RelayCommand(async () => await LoadProduitsAsync());

            _ = LoadProduitsAsync();
        }

        public ObservableCollection<Produit> Produits { get; }

        private Produit _selectedProduit;
        public Produit SelectedProduit
        {
            get => _selectedProduit;
            set => SetProperty(ref _selectedProduit, value);
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                SearchProduits();
            }
        }

        public ICommand AddProduitCommand { get; }
        public ICommand EditProduitCommand { get; }
        public ICommand DeleteProduitCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadProduitsAsync()
        {
            try
            {
                IsBusy = true;
                var produits = await _produitService.GetAllProduitsAsync();

                Produits.Clear();
                foreach (var produit in produits)
                {
                    Produits.Add(produit);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des produits: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void SearchProduits()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadProduitsAsync();
                return;
            }

            try
            {
                var produits = await _produitService.SearchProduitsAsync(SearchText);

                Produits.Clear();
                foreach (var produit in produits)
                {
                    Produits.Add(produit);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void AddProduit()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'ajout de produit en cours de développement.");
        }

        private void EditProduit()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité de modification de produit en cours de développement.");
        }

        private void DeleteProduit()
        {
            if (SelectedProduit == null) return;

            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer le produit {SelectedProduit.Nom} ?"))
            {
                MessageBoxHelper.ShowInfo("Fonctionnalité de suppression en cours de développement.");
            }
        }

        private bool CanEditProduit()
        {
            return SelectedProduit != null;
        }

        private bool CanDeleteProduit()
        {
            return SelectedProduit != null;
        }
    }
}
