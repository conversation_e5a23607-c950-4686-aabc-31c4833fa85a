using System;
using System.Data;
using System.Security.Cryptography;
using System.Text;
using Dapper;
using ZinStore.Core.Enums;
using ZinStore.Core.Models;

namespace ZinStore.Data.Context
{
    /// <summary>
    /// Initialise la base de données et crée les tables
    /// </summary>
    public class DatabaseInitializer
    {
        private readonly DatabaseContext _context;

        public DatabaseInitializer(DatabaseContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Initialise la base de données
        /// </summary>
        public void Initialize()
        {
            CreateTables();
            SeedData();
        }

        /// <summary>
        /// Crée les tables de la base de données
        /// </summary>
        private void CreateTables()
        {
            using (var connection = _context.GetConnection())
            {
                // Table Utilisateurs
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Utilisateurs (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        NomUtilisateur TEXT NOT NULL UNIQUE,
                        MotDePasse TEXT NOT NULL,
                        NomComplet TEXT NOT NULL,
                        Email TEXT,
                        Telephone TEXT,
                        TypeUtilisateur INTEGER NOT NULL,
                        EstActif INTEGER NOT NULL DEFAULT 1,
                        Photo TEXT,
                        Adresse TEXT,
                        PeutGererUtilisateurs INTEGER NOT NULL DEFAULT 0,
                        PeutGererClients INTEGER NOT NULL DEFAULT 0,
                        PeutGererFournisseurs INTEGER NOT NULL DEFAULT 0,
                        PeutGererProduits INTEGER NOT NULL DEFAULT 0,
                        PeutGererVentes INTEGER NOT NULL DEFAULT 0,
                        PeutGererAchats INTEGER NOT NULL DEFAULT 0,
                        PeutGererStock INTEGER NOT NULL DEFAULT 0,
                        PeutGererComptabilite INTEGER NOT NULL DEFAULT 0,
                        PeutVoirRapports INTEGER NOT NULL DEFAULT 0,
                        PeutGererParametres INTEGER NOT NULL DEFAULT 0,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT
                    )");

                // Table Clients
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Clients (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CodeClient TEXT NOT NULL UNIQUE,
                        Nom TEXT NOT NULL,
                        Prenom TEXT,
                        RaisonSociale TEXT,
                        Adresse TEXT,
                        Ville TEXT,
                        CodePostal TEXT,
                        Telephone TEXT,
                        Mobile TEXT,
                        Email TEXT,
                        NumeroRegistreCommerce TEXT,
                        NumeroIdentificationFiscale TEXT,
                        LimiteCredit DECIMAL NOT NULL DEFAULT 0,
                        SoldeCompte DECIMAL NOT NULL DEFAULT 0,
                        EstActif INTEGER NOT NULL DEFAULT 1,
                        Notes TEXT,
                        PersonneContact TEXT,
                        FonctionContact TEXT,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT
                    )");

                // Table Fournisseurs
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Fournisseurs (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CodeFournisseur TEXT NOT NULL UNIQUE,
                        Nom TEXT NOT NULL,
                        RaisonSociale TEXT,
                        Adresse TEXT,
                        Ville TEXT,
                        CodePostal TEXT,
                        Pays TEXT,
                        Telephone TEXT,
                        Fax TEXT,
                        Email TEXT,
                        SiteWeb TEXT,
                        NumeroRegistreCommerce TEXT,
                        NumeroIdentificationFiscale TEXT,
                        LimiteCredit DECIMAL NOT NULL DEFAULT 0,
                        SoldeCompte DECIMAL NOT NULL DEFAULT 0,
                        EstActif INTEGER NOT NULL DEFAULT 1,
                        Notes TEXT,
                        PersonneContact TEXT,
                        FonctionContact TEXT,
                        TelephoneContact TEXT,
                        EmailContact TEXT,
                        DelaiPaiement INTEGER NOT NULL DEFAULT 30,
                        ConditionsPaiement TEXT,
                        RemiseHabituelle DECIMAL NOT NULL DEFAULT 0,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT
                    )");

                // Table Categories
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Categories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CodeCategorie TEXT NOT NULL UNIQUE,
                        Nom TEXT NOT NULL,
                        Description TEXT,
                        CategorieParentId INTEGER,
                        EstActive INTEGER NOT NULL DEFAULT 1,
                        Image TEXT,
                        OrdreAffichage INTEGER NOT NULL DEFAULT 0,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT,
                        FOREIGN KEY (CategorieParentId) REFERENCES Categories(Id)
                    )");

                // Table Produits
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Produits (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CodeProduit TEXT NOT NULL UNIQUE,
                        CodeBarre TEXT,
                        Nom TEXT NOT NULL,
                        Description TEXT,
                        CategorieId INTEGER NOT NULL,
                        FournisseurId INTEGER,
                        Unite TEXT NOT NULL DEFAULT 'Pièce',
                        PrixAchat DECIMAL NOT NULL,
                        PrixVente DECIMAL NOT NULL,
                        PrixVenteGros DECIMAL,
                        TauxTVA DECIMAL NOT NULL DEFAULT 19,
                        StockMinimum DECIMAL NOT NULL DEFAULT 0,
                        StockMaximum DECIMAL NOT NULL DEFAULT 0,
                        StockActuel DECIMAL NOT NULL DEFAULT 0,
                        StockReserve DECIMAL NOT NULL DEFAULT 0,
                        EstActif INTEGER NOT NULL DEFAULT 1,
                        EstPerissable INTEGER NOT NULL DEFAULT 0,
                        DureeConservation INTEGER NOT NULL DEFAULT 0,
                        Image TEXT,
                        Poids DECIMAL NOT NULL DEFAULT 0,
                        Dimensions TEXT,
                        Emplacement TEXT,
                        Notes TEXT,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT,
                        FOREIGN KEY (CategorieId) REFERENCES Categories(Id),
                        FOREIGN KEY (FournisseurId) REFERENCES Fournisseurs(Id)
                    )");

                // Table Ventes
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Ventes (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        NumeroFacture TEXT NOT NULL UNIQUE,
                        DateVente DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        ClientId INTEGER,
                        NomClient TEXT,
                        UtilisateurId INTEGER NOT NULL,
                        Statut INTEGER NOT NULL DEFAULT 1,
                        SousTotal DECIMAL NOT NULL DEFAULT 0,
                        MontantTVA DECIMAL NOT NULL DEFAULT 0,
                        MontantRemise DECIMAL NOT NULL DEFAULT 0,
                        MontantTotal DECIMAL NOT NULL DEFAULT 0,
                        MontantPaye DECIMAL NOT NULL DEFAULT 0,
                        MontantRestant DECIMAL NOT NULL DEFAULT 0,
                        ModePaiement TEXT,
                        DateEcheance DATETIME,
                        Notes TEXT,
                        EstComptant INTEGER NOT NULL DEFAULT 1,
                        EstLivree INTEGER NOT NULL DEFAULT 0,
                        DateLivraison DATETIME,
                        AdresseLivraison TEXT,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT,
                        FOREIGN KEY (ClientId) REFERENCES Clients(Id),
                        FOREIGN KEY (UtilisateurId) REFERENCES Utilisateurs(Id)
                    )");

                // Table VentesDetails
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS VentesDetails (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        VenteId INTEGER NOT NULL,
                        ProduitId INTEGER NOT NULL,
                        Quantite DECIMAL NOT NULL,
                        PrixUnitaire DECIMAL NOT NULL,
                        PrixAchat DECIMAL NOT NULL DEFAULT 0,
                        TauxTVA DECIMAL NOT NULL DEFAULT 0,
                        MontantTVA DECIMAL NOT NULL DEFAULT 0,
                        TauxRemise DECIMAL NOT NULL DEFAULT 0,
                        MontantRemise DECIMAL NOT NULL DEFAULT 0,
                        SousTotal DECIMAL NOT NULL DEFAULT 0,
                        Total DECIMAL NOT NULL DEFAULT 0,
                        Notes TEXT,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT,
                        FOREIGN KEY (VenteId) REFERENCES Ventes(Id),
                        FOREIGN KEY (ProduitId) REFERENCES Produits(Id)
                    )");

                // Table Achats
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Achats (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        NumeroFacture TEXT NOT NULL UNIQUE,
                        NumeroFactureFournisseur TEXT,
                        DateAchat DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        FournisseurId INTEGER NOT NULL,
                        UtilisateurId INTEGER NOT NULL,
                        Statut INTEGER NOT NULL DEFAULT 1,
                        SousTotal DECIMAL NOT NULL DEFAULT 0,
                        MontantTVA DECIMAL NOT NULL DEFAULT 0,
                        MontantRemise DECIMAL NOT NULL DEFAULT 0,
                        MontantTotal DECIMAL NOT NULL DEFAULT 0,
                        MontantPaye DECIMAL NOT NULL DEFAULT 0,
                        MontantRestant DECIMAL NOT NULL DEFAULT 0,
                        ModePaiement TEXT,
                        DateEcheance DATETIME,
                        Notes TEXT,
                        EstComptant INTEGER NOT NULL DEFAULT 0,
                        EstRecue INTEGER NOT NULL DEFAULT 0,
                        DateReception DATETIME,
                        BonCommande TEXT,
                        BonLivraison TEXT,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT,
                        FOREIGN KEY (FournisseurId) REFERENCES Fournisseurs(Id),
                        FOREIGN KEY (UtilisateurId) REFERENCES Utilisateurs(Id)
                    )");

                // Table AchatsDetails
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS AchatsDetails (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        AchatId INTEGER NOT NULL,
                        ProduitId INTEGER NOT NULL,
                        Quantite DECIMAL NOT NULL,
                        PrixUnitaire DECIMAL NOT NULL,
                        TauxTVA DECIMAL NOT NULL DEFAULT 0,
                        MontantTVA DECIMAL NOT NULL DEFAULT 0,
                        TauxRemise DECIMAL NOT NULL DEFAULT 0,
                        MontantRemise DECIMAL NOT NULL DEFAULT 0,
                        SousTotal DECIMAL NOT NULL DEFAULT 0,
                        Total DECIMAL NOT NULL DEFAULT 0,
                        QuantiteRecue DECIMAL NOT NULL DEFAULT 0,
                        Notes TEXT,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT,
                        FOREIGN KEY (AchatId) REFERENCES Achats(Id),
                        FOREIGN KEY (ProduitId) REFERENCES Produits(Id)
                    )");

                // Table Stock
                connection.Execute(@"
                    CREATE TABLE IF NOT EXISTS Stock (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ProduitId INTEGER NOT NULL UNIQUE,
                        QuantiteDisponible DECIMAL NOT NULL DEFAULT 0,
                        QuantiteReservee DECIMAL NOT NULL DEFAULT 0,
                        QuantiteMinimum DECIMAL NOT NULL DEFAULT 0,
                        QuantiteMaximum DECIMAL NOT NULL DEFAULT 0,
                        CoutMoyenPondere DECIMAL NOT NULL DEFAULT 0,
                        ValeurStock DECIMAL NOT NULL DEFAULT 0,
                        DateDernierMouvement DATETIME,
                        DateDernierInventaire DATETIME,
                        Emplacement TEXT,
                        Notes TEXT,
                        DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        DateModification DATETIME,
                        EstSupprime INTEGER NOT NULL DEFAULT 0,
                        UtilisateurCreation TEXT,
                        UtilisateurModification TEXT,
                        FOREIGN KEY (ProduitId) REFERENCES Produits(Id)
                    )");
            }
        }

        /// <summary>
        /// Insère les données initiales
        /// </summary>
        private void SeedData()
        {
            using (var connection = _context.GetConnection())
            {
                // Vérifier si l'utilisateur admin existe déjà
                var adminExists = connection.QueryFirstOrDefault<int>(
                    "SELECT COUNT(*) FROM Utilisateurs WHERE NomUtilisateur = @username",
                    new { username = "admin" });

                if (adminExists == 0)
                {
                    // Créer l'utilisateur administrateur par défaut
                    var adminUser = new Utilisateur
                    {
                        NomUtilisateur = "admin",
                        MotDePasse = HashPassword("admin123"), // Utiliser la même méthode que PasswordHelper
                        NomComplet = "Administrateur",
                        Email = "<EMAIL>",
                        TypeUtilisateur = TypeUtilisateur.Administrateur,
                        EstActif = true,
                        PeutGererUtilisateurs = true,
                        PeutGererClients = true,
                        PeutGererFournisseurs = true,
                        PeutGererProduits = true,
                        PeutGererVentes = true,
                        PeutGererAchats = true,
                        PeutGererStock = true,
                        PeutGererComptabilite = true,
                        PeutVoirRapports = true,
                        PeutGererParametres = true,
                        UtilisateurCreation = "System"
                    };

                    connection.Execute(@"
                        INSERT INTO Utilisateurs (
                            NomUtilisateur, MotDePasse, NomComplet, Email, TypeUtilisateur, EstActif,
                            PeutGererUtilisateurs, PeutGererClients, PeutGererFournisseurs, PeutGererProduits,
                            PeutGererVentes, PeutGererAchats, PeutGererStock, PeutGererComptabilite,
                            PeutVoirRapports, PeutGererParametres, UtilisateurCreation
                        ) VALUES (
                            @NomUtilisateur, @MotDePasse, @NomComplet, @Email, @TypeUtilisateur, @EstActif,
                            @PeutGererUtilisateurs, @PeutGererClients, @PeutGererFournisseurs, @PeutGererProduits,
                            @PeutGererVentes, @PeutGererAchats, @PeutGererStock, @PeutGererComptabilite,
                            @PeutVoirRapports, @PeutGererParametres, @UtilisateurCreation
                        )", adminUser);
                }
            }
        }

        /// <summary>
        /// Hache le mot de passe en utilisant la même méthode que PasswordHelper
        /// </summary>
        private string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return string.Empty;

            using (SHA256 sha256Hash = SHA256.Create())
            {
                // Ajouter un salt pour plus de sécurité (même que PasswordHelper)
                string saltedPassword = password + "ZinStore2024!@#";

                // Calculer le hash
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));

                // Convertir en string hexadécimal
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
    }
}
