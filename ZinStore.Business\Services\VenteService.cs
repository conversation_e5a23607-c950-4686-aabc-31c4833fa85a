using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;

namespace ZinStore.Business.Services
{
    public class VenteService
    {
        private readonly VenteRepository _venteRepository;

        public VenteService(DatabaseContext context)
        {
            _venteRepository = new VenteRepository(context);
        }

        public async Task<IEnumerable<Vente>> GetAllVentesAsync()
        {
            return await _venteRepository.GetAllAsync();
        }

        public async Task<Vente> GetVenteByIdAsync(int id)
        {
            return await _venteRepository.GetByIdAsync(id);
        }

        public async Task<string> GenerateNextNumeroFactureAsync()
        {
            return await _venteRepository.GenerateNextNumeroFactureAsync("FV");
        }
    }
}
