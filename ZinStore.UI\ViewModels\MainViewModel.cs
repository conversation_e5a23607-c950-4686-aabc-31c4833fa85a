using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using ZinStore.Business.Services;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;
using ZinStore.UI.Views;
using ZinStore.UI.Views.Categories;

namespace ZinStore.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour la fenêtre principale
    /// </summary>
    public class MainViewModel : BaseViewModel
    {
        private readonly AuthenticationService _authService;
        private readonly DispatcherTimer _timer;

        public MainViewModel()
        {
            _authService = AuthenticationService.Instance;

            // Initialiser les commandes
            ShowDashboardCommand = new RelayCommand(ShowDashboard);
            ShowClientsCommand = new RelayCommand(ShowClients);
            ShowFournisseursCommand = new RelayCommand(ShowFournisseurs);
            ShowProduitsCommand = new RelayCommand(ShowProduits);
            ShowCategoriesCommand = new RelayCommand(ShowCategories);
            ShowVentesCommand = new RelayCommand(ShowVentes);
            ShowAchatsCommand = new RelayCommand(ShowAchats);
            ShowStockCommand = new RelayCommand(ShowStock);
            ShowRapportsCommand = new RelayCommand(ShowRapports);
            LogoutCommand = new RelayCommand(Logout);

            // Initialiser le timer pour l'heure
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += (s, e) => CurrentDateTime = DateTime.Now;
            _timer.Start();

            // Afficher le tableau de bord par défaut
            ShowDashboard();

            // Initialiser les informations utilisateur
            UpdateUserInfo();
        }

        private object _currentView;
        public object CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        private string _currentUserName;
        public string CurrentUserName
        {
            get => _currentUserName;
            set => SetProperty(ref _currentUserName, value);
        }

        private DateTime _currentDateTime = DateTime.Now;
        public DateTime CurrentDateTime
        {
            get => _currentDateTime;
            set => SetProperty(ref _currentDateTime, value);
        }

        private string _statusMessage = "Prêt";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        // Commandes
        public ICommand ShowDashboardCommand { get; }
        public ICommand ShowClientsCommand { get; }
        public ICommand ShowFournisseursCommand { get; }
        public ICommand ShowProduitsCommand { get; }
        public ICommand ShowCategoriesCommand { get; }
        public ICommand ShowVentesCommand { get; }
        public ICommand ShowAchatsCommand { get; }
        public ICommand ShowStockCommand { get; }
        public ICommand ShowRapportsCommand { get; }
        public ICommand LogoutCommand { get; }

        private void ShowDashboard()
        {
            CurrentView = new Dashboard();
            StatusMessage = "Tableau de bord";
        }

        private void ShowClients()
        {
            CurrentView = new ClientsView();
            StatusMessage = "Gestion des clients";
        }

        private void ShowFournisseurs()
        {
            CurrentView = new FournisseursView();
            StatusMessage = "Gestion des fournisseurs";
        }

        private void ShowProduits()
        {
            CurrentView = new ProduitsView();
            StatusMessage = "Gestion des produits";
        }

        private void ShowCategories()
        {
            CurrentView = new CategoriesView();
            StatusMessage = "Gestion des catégories";
        }

        private void ShowVentes()
        {
            CurrentView = new VentesView();
            StatusMessage = "Gestion des ventes";
        }

        private void ShowAchats()
        {
            CurrentView = new AchatsView();
            StatusMessage = "Gestion des achats";
        }

        private void ShowStock()
        {
            CurrentView = new StockView();
            StatusMessage = "Gestion du stock";
        }

        private void ShowRapports()
        {
            StatusMessage = "Rapports et statistiques";
            MessageBoxHelper.ShowInfo("Module des rapports en cours de développement.");
        }

        private void Logout()
        {
            if (MessageBoxHelper.ShowConfirmation("Êtes-vous sûr de vouloir vous déconnecter ?"))
            {
                _authService.Logout();
                _timer.Stop();

                // Ouvrir la fenêtre de connexion
                var loginWindow = new LoginWindow();
                loginWindow.Show();

                // Fermer la fenêtre principale
                foreach (Window window in Application.Current.Windows)
                {
                    if (window is MainWindow)
                    {
                        window.Close();
                        break;
                    }
                }
            }
        }

        private void UpdateUserInfo()
        {
            if (_authService.IsAuthenticated)
            {
                CurrentUserName = _authService.CurrentUser.NomComplet;
            }
            else
            {
                CurrentUserName = "Utilisateur non connecté";
            }
        }
    }
}
