<Window x:Class="ZinStore.UI.Views.DatabaseConnectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Configuration de la Base de Données - ZinStore"
        Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Database"
                                   Width="48" Height="48"
                                   HorizontalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="Configuration de la Base de Données"
                      FontSize="20" FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,10,0,0"/>
            <TextBlock Text="Configurez la connexion à votre base de données SQLite"
                      FontSize="14" Opacity="0.7"
                      HorizontalAlignment="Center"
                      Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Formulaire de configuration -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,-10" Grid.RowSpan="2">
            <StackPanel>
                <!-- Type de base de données -->
                <TextBlock Text="Type de Base de Données"
                          FontWeight="SemiBold"
                          Margin="0,0,0,10"/>
                <ComboBox x:Name="DatabaseTypeComboBox"
                         materialDesign:HintAssist.Hint="Sélectionnez le type"
                         Style="{StaticResource MaterialDesignComboBox}"
                         Margin="0,0,0,20"
                         SelectedIndex="0">
                    <ComboBoxItem Content="SQLite (Fichier local)"/>
                    <ComboBoxItem Content="SQL Server"/>
                    <ComboBoxItem Content="MySQL"/>
                    <ComboBoxItem Content="PostgreSQL"/>
                </ComboBox>

                <!-- Configuration SQLite -->
                <StackPanel x:Name="SQLitePanel" Visibility="Visible">
                    <TextBlock Text="Fichier de Base de Données"
                              FontWeight="SemiBold"
                              Margin="0,0,0,10"/>

                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox x:Name="DatabasePathTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="Chemin du fichier de base de données"
                                Style="{StaticResource MaterialDesignTextBox}"
                                Text=".\Data\ZinStore.db"
                                Margin="0,0,10,0"/>

                        <Button Grid.Column="1"
                               Content="Parcourir"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Click="BrowseDatabase_Click"
                               Width="100"/>
                    </Grid>

                    <CheckBox x:Name="CreateIfNotExistsCheckBox"
                             Content="Créer la base de données si elle n'existe pas"
                             IsChecked="True"
                             Margin="0,0,0,15"/>
                </StackPanel>

                <!-- Configuration SQL Server -->
                <StackPanel x:Name="SqlServerPanel" Visibility="Collapsed">
                    <TextBox x:Name="ServerNameTextBox"
                            materialDesign:HintAssist.Hint="Nom du serveur"
                            Style="{StaticResource MaterialDesignTextBox}"
                            Margin="0,0,0,15"/>

                    <TextBox x:Name="DatabaseNameTextBox"
                            materialDesign:HintAssist.Hint="Nom de la base de données"
                            Style="{StaticResource MaterialDesignTextBox}"
                            Margin="0,0,0,15"/>

                    <CheckBox x:Name="UseWindowsAuthCheckBox"
                             Content="Utiliser l'authentification Windows"
                             IsChecked="True"
                             Margin="0,0,0,15"/>

                    <StackPanel x:Name="SqlAuthPanel" Visibility="Collapsed">
                        <TextBox x:Name="UsernameTextBox"
                                materialDesign:HintAssist.Hint="Nom d'utilisateur"
                                Style="{StaticResource MaterialDesignTextBox}"
                                Margin="0,0,0,15"/>

                        <PasswordBox x:Name="PasswordBox"
                                    materialDesign:HintAssist.Hint="Mot de passe"
                                    Style="{StaticResource MaterialDesignPasswordBox}"
                                    Margin="0,0,0,15"/>
                    </StackPanel>
                </StackPanel>

                <!-- Chaîne de connexion générée -->
                <TextBlock Text="Chaîne de Connexion"
                          FontWeight="SemiBold"
                          Margin="0,20,0,10"/>

                <TextBox x:Name="ConnectionStringTextBox"
                        materialDesign:HintAssist.Hint="Chaîne de connexion générée"
                        Style="{StaticResource MaterialDesignTextBox}"
                        IsReadOnly="True"
                        TextWrapping="Wrap"
                        MinLines="2"
                        MaxLines="4"
                        Margin="0,0,0,20"/>

                <!-- Gestion du fichier de connexion -->
                <Expander Header="Fichier de Connexion de Secours"
                         Margin="0,10,0,20"
                         materialDesign:ExpanderAssist.HorizontalHeaderPadding="0">
                    <StackPanel Margin="0,10,0,0">
                        <TextBlock Text="Le système peut utiliser un fichier texte comme source de connexion de secours."
                                  FontSize="12" Opacity="0.7"
                                  TextWrapping="Wrap"
                                  Margin="0,0,0,10"/>

                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Button Content="Charger depuis Fichier"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Click="LoadFromFile_Click"
                                   Margin="0,0,10,0"/>

                            <Button Content="Sauvegarder vers Fichier"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Click="SaveToFile_Click"
                                   Margin="0,0,10,0"/>

                            <Button Content="Info Fichier"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Click="FileInfo_Click"/>
                        </StackPanel>

                        <Border x:Name="FileStatusBorder"
                               Background="#E8F5E8"
                               BorderBrush="#4CAF50"
                               BorderThickness="1"
                               CornerRadius="5"
                               Padding="10"
                               Visibility="Collapsed">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <materialDesign:PackIcon x:Name="FileStatusIcon"
                                                           Kind="FileCheck"
                                                           Width="16" Height="16"
                                                           Foreground="#4CAF50"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock x:Name="FileStatusText"
                                              Text="Statut du fichier"
                                              FontSize="12"
                                              FontWeight="SemiBold"
                                              VerticalAlignment="Center"/>
                                </StackPanel>
                                <TextBlock x:Name="FileDetailsText"
                                          Text="Détails du fichier"
                                          FontSize="11"
                                          Opacity="0.8"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Expander>

                <!-- Boutons de test -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                    <Button Content="Générer"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="GenerateConnectionString_Click"
                           Margin="0,0,10,0"/>

                    <Button Content="Tester la Connexion"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Click="TestConnection_Click"/>
                </StackPanel>

                <!-- Statut de la connexion -->
                <Border x:Name="StatusBorder"
                       Background="LightGray"
                       CornerRadius="5"
                       Padding="10"
                       Visibility="Collapsed">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon x:Name="StatusIcon"
                                               Kind="Information"
                                               Width="20" Height="20"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock x:Name="StatusText"
                                  Text="Statut de la connexion"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </materialDesign:Card>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2"
                   Orientation="Horizontal"
                   HorizontalAlignment="Right"
                   Margin="0,20,0,0">
            <Button Content="Annuler"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click"
                   Width="100"
                   Margin="0,0,10,0"/>

            <Button Content="Sauvegarder"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="Save_Click"
                   Width="120"/>
        </StackPanel>
    </Grid>
</Window>
